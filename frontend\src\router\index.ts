import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },
  {
    path: '/',
    component: () => import('@/layout/index.vue'),
    redirect: '/dashboard',
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/DeptHeadDashboard.vue'),
        meta: {
          title: '工作台',
          requiresAuth: true
        }
      },
      // 支出控制模块
      {
        path: 'expense',
        name: 'Expense',
        meta: {
          title: '支出控制',
          requiresAuth: true
        },
        children: [
          {
            path: 'claims',
            name: 'ExpenseClaimList',
            component: () => import('@/views/expense/ExpenseClaimList.vue'),
            meta: {
              title: '报销申请管理',
              requiresAuth: true
            }
          }
        ]
      },
      // 审批模块
      {
        path: 'approval',
        name: 'Approval',
        meta: {
          title: '审批管理',
          requiresAuth: true
        },
        children: [
          {
            path: 'tasks',
            name: 'ApprovalList',
            component: () => import('@/views/approval/ApprovalList.vue'),
            meta: {
              title: '待办审批',
              requiresAuth: true
            }
          }
        ]
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory('/hospital/'),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token')
  const basePath = router.options.history.base || '/'

  if (to.meta.requiresAuth && !token) {
    // 使用完整路径进行重定向
    next({ path: '/login', replace: true })
  } else if (to.path === '/login' && token) {
    // 已登录用户访问登录页面，重定向到工作台
    next({ path: '/dashboard', replace: true })
  } else {
    next()
  }
})

export default router