<template>
  <div class="approval-list">
    <div class="page-header">
      <h2>待办审批</h2>
      <a-statistic-group>
        <a-statistic title="待处理" :value="statistics.pending" />
        <a-statistic title="今日已处理" :value="statistics.todayProcessed" />
        <a-statistic title="本月已处理" :value="statistics.monthProcessed" />
      </a-statistic-group>
    </div>

    <!-- 筛选器 -->
    <div class="filter-bar">
      <a-space>
        <a-select v-model:value="filterType" placeholder="业务类型" style="width: 120px" allowClear>
          <a-select-option value="expense_claim">报销申请</a-select-option>
          <a-select-option value="contract">合同审批</a-select-option>
          <a-select-option value="pre_approval">事前申请</a-select-option>
        </a-select>
        <a-select v-model:value="filterUrgent" placeholder="紧急程度" style="width: 120px" allowClear>
          <a-select-option value="normal">普通</a-select-option>
          <a-select-option value="urgent">紧急</a-select-option>
          <a-select-option value="emergency">特急</a-select-option>
        </a-select>
        <a-input v-model:value="filterKeyword" placeholder="搜索标题或申请人" style="width: 200px" />
        <a-button type="primary" @click="loadApprovalTasks">搜索</a-button>
      </a-space>
    </div>

    <!-- 审批任务列表 -->
    <div class="approval-tasks">
      <a-list
        :data-source="approvalTasks"
        :loading="loading"
        item-layout="vertical"
      >
        <template #renderItem="{ item }">
          <a-list-item>
            <template #actions>
              <a-space>
                <a-button type="primary" size="small" @click="handleApproval(item, 'approve')">
                  <CheckOutlined />
                  通过
                </a-button>
                <a-button danger size="small" @click="handleApproval(item, 'reject')">
                  <CloseOutlined />
                  驳回
                </a-button>
                <a-button size="small" @click="viewDetail(item)">
                  <EyeOutlined />
                  查看详情
                </a-button>
              </a-space>
            </template>
            
            <template #extra>
              <div class="task-meta">
                <a-tag :color="getUrgentColor(item.urgentLevel)">
                  {{ getUrgentText(item.urgentLevel) }}
                </a-tag>
                <div class="task-amount" v-if="item.amount">
                  ¥{{ item.amount.toFixed(2) }}
                </div>
              </div>
            </template>

            <a-list-item-meta>
              <template #title>
                <a @click="viewDetail(item)">{{ item.title }}</a>
              </template>
              <template #description>
                <div class="task-info">
                  <a-space>
                    <span>申请人: {{ item.applicantName }}</span>
                    <span>类型: {{ getBusinessTypeText(item.businessType) }}</span>
                    <span>提交时间: {{ formatTime(item.submissionTime) }}</span>
                  </a-space>
                </div>
              </template>
            </a-list-item-meta>

            <div class="task-summary">
              {{ item.summary }}
            </div>
          </a-list-item>
        </template>
      </a-list>
    </div>

    <!-- 审批弹窗 -->
    <a-modal
      v-model:visible="approvalModalVisible"
      :title="approvalModalTitle"
      width="600px"
      :confirm-loading="approvalLoading"
      @ok="submitApproval"
      @cancel="cancelApproval"
    >
      <div v-if="currentTask">
        <div class="approval-info">
          <h4>{{ currentTask.title }}</h4>
          <p><strong>申请人:</strong> {{ currentTask.applicantName }}</p>
          <p><strong>申请金额:</strong> ¥{{ currentTask.amount?.toFixed(2) || '0.00' }}</p>
          <p><strong>申请事由:</strong> {{ currentTask.summary }}</p>
        </div>
        
        <a-divider />
        
        <a-form layout="vertical">
          <a-form-item label="审批意见">
            <a-textarea
              v-model:value="approvalComment"
              placeholder="请输入审批意见（可选）"
              :rows="4"
              :maxlength="500"
              show-count
            />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- 详情弹窗 -->
    <a-modal
      v-model:visible="detailModalVisible"
      title="申请详情"
      width="800px"
      :footer="null"
    >
      <div v-if="currentTask">
        <!-- 基本信息 -->
        <a-descriptions title="基本信息" :column="2" bordered>
          <a-descriptions-item label="申请标题">{{ currentTask.title }}</a-descriptions-item>
          <a-descriptions-item label="申请人">{{ currentTask.applicantName }}</a-descriptions-item>
          <a-descriptions-item label="业务类型">{{ getBusinessTypeText(currentTask.businessType) }}</a-descriptions-item>
          <a-descriptions-item label="申请金额">¥{{ currentTask.amount?.toFixed(2) || '0.00' }}</a-descriptions-item>
          <a-descriptions-item label="紧急程度">
            <a-tag :color="getUrgentColor(currentTask.urgentLevel)">
              {{ getUrgentText(currentTask.urgentLevel) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="提交时间">{{ formatTime(currentTask.submissionTime) }}</a-descriptions-item>
        </a-descriptions>

        <!-- 工作流图表 -->
        <div style="margin-top: 24px;">
          <WorkflowChart
            :instance-id="currentTask.workflowInstanceId"
            :show-history="true"
            title="审批流程"
          />
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { CheckOutlined, CloseOutlined, EyeOutlined } from '@ant-design/icons-vue'
import { workflowApi } from '@/api/workflow'
import WorkflowChart from '@/components/WorkflowChart.vue'
import type { ApprovalTask } from '@/types/workflow'

// 响应式数据
const loading = ref(false)
const approvalTasks = ref<ApprovalTask[]>([])
const approvalModalVisible = ref(false)
const detailModalVisible = ref(false)
const approvalLoading = ref(false)
const currentTask = ref<ApprovalTask | null>(null)
const currentAction = ref<'approve' | 'reject'>('approve')
const approvalComment = ref('')

// 筛选条件
const filterType = ref<string>()
const filterUrgent = ref<string>()
const filterKeyword = ref('')

// 统计数据
const statistics = reactive({
  pending: 0,
  todayProcessed: 0,
  monthProcessed: 0
})

// 计算属性
const approvalModalTitle = computed(() => {
  return currentAction.value === 'approve' ? '审批通过' : '审批驳回'
})

// 获取紧急程度颜色
const getUrgentColor = (level?: string) => {
  const colorMap: Record<string, string> = {
    normal: 'default',
    urgent: 'orange',
    emergency: 'red'
  }
  return colorMap[level || 'normal']
}

// 获取紧急程度文本
const getUrgentText = (level?: string) => {
  const textMap: Record<string, string> = {
    normal: '普通',
    urgent: '紧急',
    emergency: '特急'
  }
  return textMap[level || 'normal']
}

// 获取业务类型文本
const getBusinessTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    expense_claim: '报销申请',
    contract: '合同审批',
    pre_approval: '事前申请',
    purchase: '采购申请'
  }
  return textMap[type] || type
}

// 格式化时间
const formatTime = (time: string) => {
  return new Date(time).toLocaleString()
}

// 加载审批任务
const loadApprovalTasks = async () => {
  loading.value = true
  try {
    // 模拟数据，实际应该调用API
    approvalTasks.value = [
      {
        id: '1',
        type: 'expense_claim',
        title: '办公用品采购报销申请',
        applicant: 'user1',
        applicantName: '张三',
        summary: '购买办公用品，包括打印纸、文具等',
        amount: 1500.00,
        submissionTime: new Date().toISOString(),
        urgentLevel: 'normal',
        businessId: 'claim1',
        businessType: 'expense_claim',
        workflowInstanceId: 'instance1',
        currentNodeId: 'dept_head',
        link: '/expense/claim/claim1'
      },
      {
        id: '2',
        type: 'expense_claim',
        title: '差旅费报销申请',
        applicant: 'user2',
        applicantName: '李四',
        summary: '出差北京产生的交通费和住宿费',
        amount: 3200.00,
        submissionTime: new Date(Date.now() - 86400000).toISOString(),
        urgentLevel: 'urgent',
        businessId: 'claim2',
        businessType: 'expense_claim',
        workflowInstanceId: 'instance2',
        currentNodeId: 'dept_head',
        link: '/expense/claim/claim2'
      }
    ]
    
    // 更新统计数据
    statistics.pending = approvalTasks.value.length
    statistics.todayProcessed = 5
    statistics.monthProcessed = 23
  } catch (error) {
    console.error('加载审批任务失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理审批
const handleApproval = (task: ApprovalTask, action: 'approve' | 'reject') => {
  currentTask.value = task
  currentAction.value = action
  approvalComment.value = ''
  approvalModalVisible.value = true
}

// 提交审批
const submitApproval = async () => {
  if (!currentTask.value) return
  
  approvalLoading.value = true
  try {
    await workflowApi.processApproval({
      instanceId: currentTask.value.workflowInstanceId,
      action: currentAction.value,
      comment: approvalComment.value
    })
    
    message.success(`${currentAction.value === 'approve' ? '审批通过' : '审批驳回'}成功`)
    approvalModalVisible.value = false
    loadApprovalTasks() // 重新加载数据
  } catch (error) {
    console.error('审批失败:', error)
    message.error('审批失败')
  } finally {
    approvalLoading.value = false
  }
}

// 取消审批
const cancelApproval = () => {
  approvalModalVisible.value = false
  currentTask.value = null
  approvalComment.value = ''
}

// 查看详情
const viewDetail = (task: ApprovalTask) => {
  currentTask.value = task
  detailModalVisible.value = true
}

// 初始化
onMounted(() => {
  loadApprovalTasks()
})
</script>

<style scoped>
.approval-list {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.filter-bar {
  background: #fafafa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.approval-tasks {
  background: #fff;
  border-radius: 6px;
}

.task-meta {
  text-align: right;
}

.task-amount {
  font-size: 16px;
  font-weight: 500;
  color: #1890ff;
  margin-top: 4px;
}

.task-info {
  color: #666;
}

.task-summary {
  margin-top: 8px;
  color: #666;
}

.approval-info h4 {
  margin-bottom: 16px;
}

.approval-info p {
  margin-bottom: 8px;
}
</style>
