import request from './request'

// 工作流相关API接口

export interface ProcessApprovalRequest {
  instanceId: string
  action: 'approve' | 'reject'
  comment?: string
}

export interface WorkflowNodeResponse {
  id: string
  label: string
  status: 'pending' | 'processing' | 'completed' | 'rejected'
  assignee: string
}

export interface WorkflowEdgeResponse {
  source: string
  target: string
}

export interface WorkflowInstanceResponse {
  id: string
  definitionKey: string
  businessId: string
  businessType: string
  status: string
  startTime: string
  endTime?: string
  nodes: WorkflowNodeResponse[]
  edges: WorkflowEdgeResponse[]
}

export interface WorkflowPreviewResponse {
  nodes: WorkflowNodeResponse[]
  edges: WorkflowEdgeResponse[]
}

export interface StartWorkflowRequest {
  definitionKey: string
  businessId: string
  businessType: string
  startUserId: string
  variables?: Record<string, any>
}

export interface StartWorkflowResponse {
  instanceId: string
  currentNodeId: string
  status: string
}

export interface TerminateWorkflowRequest {
  reason: string
}

// 工作流API
export const workflowApi = {
  // 处理审批
  processApproval(data: ProcessApprovalRequest) {
    return request.post('/approvals', data)
  },

  // 获取工作流实例详情
  getWorkflowInstance(instanceId: string) {
    return request.get<WorkflowInstanceResponse>(`/workflow/instance/${instanceId}`)
  },

  // 获取审批流程预览
  getWorkflowPreview(params: {
    type: string
    amount?: number
    departmentId?: string
  }) {
    return request.get<WorkflowPreviewResponse>('/workflow/preview', { params })
  },

  // 终止工作流实例
  terminateWorkflow(instanceId: string, data: TerminateWorkflowRequest) {
    return request.post(`/workflow/instance/${instanceId}/terminate`, data)
  },

  // 启动工作流
  startWorkflow(data: StartWorkflowRequest) {
    return request.post<StartWorkflowResponse>('/workflow/start', data)
  }
}

export default workflowApi
